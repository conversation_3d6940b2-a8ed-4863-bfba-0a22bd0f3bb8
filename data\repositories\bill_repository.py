"""
账单仓库实现
"""

from typing import List, Optional
from datetime import datetime, date
from sqlalchemy.orm import sessionmaker

from data.models.bill import Bill
from core.exceptions import DatabaseException


class BillRepository:
    """账单仓库"""
    
    def __init__(self, session_factory: sessionmaker):
        self.session_factory = session_factory
    
    def create(self, bill: Bill) -> Bill:
        """创建账单"""
        with self.session_factory() as session:
            try:
                session.add(bill)
                session.commit()
                session.refresh(bill)
                return bill
            except Exception as e:
                session.rollback()
                raise DatabaseException(f"创建账单失败: {e}")
    
    def get_by_id(self, bill_id: int) -> Optional[Bill]:
        """根据ID获取账单"""
        with self.session_factory() as session:
            return session.query(Bill).filter(
                Bill.id == bill_id,
                Bill.is_deleted == False
            ).first()
    
    def get_all(self) -> List[Bill]:
        """获取所有账单"""
        with self.session_factory() as session:
            return session.query(Bill).filter(Bill.is_deleted == False).all()
    
    def update(self, bill: Bill) -> Bill:
        """更新账单"""
        with self.session_factory() as session:
            try:
                session.merge(bill)
                session.commit()
                return bill
            except Exception as e:
                session.rollback()
                raise DatabaseException(f"更新账单失败: {e}")
    
    def delete(self, bill_id: int) -> bool:
        """删除账单（软删除）"""
        with self.session_factory() as session:
            try:
                bill = session.query(Bill).filter(Bill.id == bill_id).first()
                if bill:
                    bill.is_deleted = True
                    session.commit()
                    return True
                return False
            except Exception as e:
                session.rollback()
                raise DatabaseException(f"删除账单失败: {e}")
