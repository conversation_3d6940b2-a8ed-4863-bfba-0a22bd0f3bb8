#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
钱迹Mini - 主应用入口
基于PySide6的跨平台记账应用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, QLibraryInfo
from PySide6.QtGui import QIcon

# 临时简化导入，避免循环依赖
# from core.container import Container
from presentation.views.main_window import MainWindow
# from config.settings import Settings
from core.utils import setup_logging


def setup_application():
    """设置应用程序基本配置"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("钱迹Mini")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("QianJi Mini")
    app.setOrganizationDomain("qianjimini.com")

    # 设置应用程序图标
    icon_path = project_root / "presentation" / "resources" / "icons" / "app_icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))

    # 设置中文本地化
    locale = QLocale.system()

    # 加载Qt自带的中文翻译
    qt_translator = QTranslator()
    qt_translator.load(locale, "qt", "_", QLibraryInfo.path(QLibraryInfo.LibraryPath.TranslationsPath))
    app.installTranslator(qt_translator)

    # 设置样式
    app.setStyle("Fusion")  # 使用Fusion样式，跨平台一致性更好

    return app


def setup_database():
    """初始化数据库"""
    from data.database.connection import DatabaseManager

    db_manager = DatabaseManager()
    db_manager.create_tables()
    return db_manager


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()

        # 创建应用程序
        app = setup_application()

        # 临时注释掉依赖注入，先测试基础功能
        # # 初始化依赖注入容器
        # container = Container()
        # container.config.from_dict(Settings().model_dump())
        # container.wire(modules=[
        #     "presentation.views.main_window",
        # ])

        # 初始化数据库
        db_manager = setup_database()

        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        # 运行应用程序
        exit_code = app.exec()

        # 清理资源
        db_manager.close()

        return exit_code

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
