"""
应用程序配置设置
"""

import os
from pathlib import Path
from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class DatabaseConfig(BaseModel):
    """数据库配置"""
    name: str = "qianji_mini.db"
    path: Optional[str] = None
    echo: bool = False  # 是否打印SQL语句
    
    @property
    def url(self) -> str:
        """获取数据库URL"""
        if self.path:
            db_path = Path(self.path) / self.name
        else:
            # 默认使用用户数据目录
            data_dir = Path.home() / ".qianji_mini"
            data_dir.mkdir(exist_ok=True)
            db_path = data_dir / self.name
        
        return f"sqlite:///{db_path}"


class UIConfig(BaseModel):
    """UI配置"""
    theme: str = "light"  # light, dark
    language: str = "zh_CN"
    window_width: int = 1200
    window_height: int = 800
    remember_window_state: bool = True


class BackupConfig(BaseModel):
    """备份配置"""
    auto_backup: bool = True
    backup_interval_days: int = 7
    max_backup_files: int = 10
    backup_path: Optional[str] = None
    
    @property
    def backup_directory(self) -> Path:
        """获取备份目录"""
        if self.backup_path:
            return Path(self.backup_path)
        else:
            return Path.home() / ".qianji_mini" / "backups"


class SyncConfig(BaseModel):
    """同步配置"""
    auto_sync: bool = False
    sync_provider: str = "local"  # local, onedrive, googledrive
    sync_interval_minutes: int = 30
    encryption_enabled: bool = True


class Settings(BaseSettings):
    """应用程序主配置"""
    
    # 应用信息
    app_name: str = "钱迹Mini"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    ui: UIConfig = Field(default_factory=UIConfig)
    backup: BackupConfig = Field(default_factory=BackupConfig)
    sync: SyncConfig = Field(default_factory=SyncConfig)
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    class Config:
        env_prefix = "QIANJI_"
        env_nested_delimiter = "__"
        case_sensitive = False
    
    @classmethod
    def load_from_file(cls, config_file: Optional[Path] = None) -> "Settings":
        """从配置文件加载设置"""
        if config_file is None:
            config_file = Path.home() / ".qianji_mini" / "config.json"
        
        if config_file.exists():
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        else:
            return cls()
    
    def save_to_file(self, config_file: Optional[Path] = None) -> None:
        """保存设置到配置文件"""
        if config_file is None:
            config_dir = Path.home() / ".qianji_mini"
            config_dir.mkdir(exist_ok=True)
            config_file = config_dir / "config.json"
        
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.model_dump(), f, ensure_ascii=False, indent=2)


# 全局设置实例
settings = Settings.load_from_file()
