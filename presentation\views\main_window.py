"""
主窗口
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QPushButton, QStatusBar,
    QMessageBox, QSplitter
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction
from loguru import logger

from core.constants import UIConstants


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("钱迹Mini - 个人记账软件")
        self.setMinimumSize(UIConstants.MIN_WINDOW_WIDTH, UIConstants.MIN_WINDOW_HEIGHT)
        self.resize(UIConstants.DEFAULT_WINDOW_WIDTH, UIConstants.DEFAULT_WINDOW_HEIGHT)
        
        # 初始化UI
        self._setup_ui()
        self._setup_menu()
        self._setup_status_bar()
        self._setup_connections()
        
        logger.info("主窗口初始化完成")
    
    def _setup_ui(self):
        """设置UI界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧导航面板
        self._setup_navigation_panel(splitter)
        
        # 右侧主内容区域
        self._setup_content_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([200, 1000])
    
    def _setup_navigation_panel(self, parent):
        """设置左侧导航面板"""
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        
        # 导航按钮
        nav_buttons = [
            ("📊 首页", self._show_dashboard),
            ("💰 记账", self._show_bill_input),
            ("📋 账单", self._show_bill_list),
            ("📈 统计", self._show_statistics),
            ("🏦 资产", self._show_assets),
            ("⚙️ 设置", self._show_settings),
        ]
        
        for text, slot in nav_buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(40)
            btn.clicked.connect(slot)
            nav_layout.addWidget(btn)
        
        nav_layout.addStretch()
        parent.addWidget(nav_widget)
    
    def _setup_content_area(self, parent):
        """设置右侧内容区域"""
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self._close_tab)
        
        # 添加默认首页选项卡
        self._add_dashboard_tab()
        
        parent.addWidget(self.tab_widget)
    
    def _add_dashboard_tab(self):
        """添加首页选项卡"""
        dashboard_widget = QWidget()
        layout = QVBoxLayout(dashboard_widget)
        
        # 欢迎信息
        welcome_label = QLabel("欢迎使用钱迹Mini！")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        layout.addWidget(welcome_label)
        
        # 快速操作按钮
        quick_actions_layout = QHBoxLayout()
        
        quick_buttons = [
            ("💰 快速记账", self._show_bill_input),
            ("📊 查看统计", self._show_statistics),
            ("🏦 管理资产", self._show_assets),
        ]
        
        for text, slot in quick_buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(60)
            btn.clicked.connect(slot)
            quick_actions_layout.addWidget(btn)
        
        layout.addLayout(quick_actions_layout)
        
        # 今日概览
        overview_label = QLabel("今日概览")
        overview_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px 0;")
        layout.addWidget(overview_label)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        stats_info = [
            ("今日支出", "¥0.00", UIConstants.ERROR_COLOR),
            ("今日收入", "¥0.00", UIConstants.SUCCESS_COLOR),
            ("账户余额", "¥0.00", UIConstants.PRIMARY_COLOR),
        ]
        
        for title, value, color in stats_info:
            stat_widget = QWidget()
            stat_layout = QVBoxLayout(stat_widget)
            
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            
            value_label = QLabel(value)
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {color};")
            
            stat_layout.addWidget(title_label)
            stat_layout.addWidget(value_label)
            
            stats_layout.addWidget(stat_widget)
        
        layout.addLayout(stats_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(dashboard_widget, "📊 首页")
    
    def _setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 导入
        import_action = QAction("导入数据(&I)", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self._import_data)
        file_menu.addAction(import_action)
        
        # 导出
        export_action = QAction("导出数据(&E)", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self._export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 备份
        backup_action = QAction("备份数据(&B)", self)
        backup_action.triggered.connect(self._backup_data)
        tools_menu.addAction(backup_action)
        
        # 恢复
        restore_action = QAction("恢复数据(&R)", self)
        restore_action.triggered.connect(self._restore_data)
        tools_menu.addAction(restore_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 显示就绪状态
        self.status_bar.showMessage("就绪")
        
        # 添加数据库状态指示器
        self.db_status_label = QLabel("数据库: 已连接")
        self.status_bar.addPermanentWidget(self.db_status_label)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 定时更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(30000)  # 30秒更新一次
    
    def _update_status(self):
        """更新状态信息"""
        # 这里可以添加状态更新逻辑
        pass
    
    def _close_tab(self, index):
        """关闭选项卡"""
        if self.tab_widget.count() > 1:  # 保留至少一个选项卡
            self.tab_widget.removeTab(index)
    
    # 导航按钮槽函数
    def _show_dashboard(self):
        """显示首页"""
        # 如果首页选项卡不存在，则添加
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == "📊 首页":
                self.tab_widget.setCurrentIndex(i)
                return
        self._add_dashboard_tab()
    
    def _show_bill_input(self):
        """显示记账界面"""
        # 检查是否已经打开了记账选项卡
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == "💰 记账":
                self.tab_widget.setCurrentIndex(i)
                return

        # 创建记账界面
        from presentation.views.bill_input_view import BillInputView
        bill_input_view = BillInputView()
        bill_input_view.bill_saved.connect(self._on_bill_saved)

        # 添加到选项卡
        index = self.tab_widget.addTab(bill_input_view, "💰 记账")
        self.tab_widget.setCurrentIndex(index)

    def _on_bill_saved(self, bill):
        """账单保存成功处理"""
        self.status_bar.showMessage(f"账单保存成功: {bill.formatted_amount}", 3000)
    
    def _show_bill_list(self):
        """显示账单列表"""
        self._show_message("账单列表", "账单列表功能正在开发中...")
    
    def _show_statistics(self):
        """显示统计界面"""
        self._show_message("统计分析", "统计分析功能正在开发中...")
    
    def _show_assets(self):
        """显示资产管理"""
        self._show_message("资产管理", "资产管理功能正在开发中...")
    
    def _show_settings(self):
        """显示设置界面"""
        self._show_message("设置", "设置功能正在开发中...")
    
    # 菜单动作槽函数
    def _import_data(self):
        """导入数据"""
        self._show_message("导入数据", "数据导入功能正在开发中...")
    
    def _export_data(self):
        """导出数据"""
        self._show_message("导出数据", "数据导出功能正在开发中...")
    
    def _backup_data(self):
        """备份数据"""
        self._show_message("备份数据", "数据备份功能正在开发中...")
    
    def _restore_data(self):
        """恢复数据"""
        self._show_message("恢复数据", "数据恢复功能正在开发中...")
    
    def _show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于钱迹Mini",
            "钱迹Mini v1.0.0\n\n"
            "一款简洁、纯粹的个人记账软件\n"
            "基于PySide6开发\n\n"
            "© 2024 钱迹Mini团队"
        )
    
    def _show_message(self, title, message):
        """显示消息对话框"""
        QMessageBox.information(self, title, message)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出钱迹Mini吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            logger.info("应用程序正在退出...")
            event.accept()
        else:
            event.ignore()
