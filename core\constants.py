"""
应用程序常量定义
"""

from enum import Enum, IntEnum


class BillType(IntEnum):
    """账单类型"""
    EXPENSE = 0  # 支出
    INCOME = 1   # 收入
    TRANSFER = 2 # 转账


class AccountType(IntEnum):
    """账户类型"""
    CASH = 0           # 现金
    BANK_CARD = 1      # 银行卡
    CREDIT_CARD = 2    # 信用卡
    RECHARGE_CARD = 3  # 充值卡
    INVESTMENT = 4     # 投资理财


class CategoryType(IntEnum):
    """分类类型"""
    EXPENSE = 0  # 支出分类
    INCOME = 1   # 收入分类


class Currency(str, Enum):
    """货币类型"""
    CNY = "CNY"  # 人民币
    USD = "USD"  # 美元
    EUR = "EUR"  # 欧元
    JPY = "JPY"  # 日元
    GBP = "GBP"  # 英镑


class DateFormat:
    """日期格式常量"""
    DATE_FORMAT = "yyyy-MM-dd"
    DATETIME_FORMAT = "yyyy-MM-dd hh:mm:ss"
    TIME_FORMAT = "hh:mm:ss"


class UIConstants:
    """UI相关常量"""
    
    # 窗口尺寸
    MIN_WINDOW_WIDTH = 800
    MIN_WINDOW_HEIGHT = 600
    DEFAULT_WINDOW_WIDTH = 1200
    DEFAULT_WINDOW_HEIGHT = 800
    
    # 颜色
    PRIMARY_COLOR = "#1976D2"
    SECONDARY_COLOR = "#424242"
    SUCCESS_COLOR = "#4CAF50"
    WARNING_COLOR = "#FF9800"
    ERROR_COLOR = "#F44336"
    
    # 字体大小
    FONT_SIZE_SMALL = 10
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    # 间距
    SPACING_SMALL = 5
    SPACING_NORMAL = 10
    SPACING_LARGE = 15
    SPACING_XLARGE = 20


class FileConstants:
    """文件相关常量"""
    
    # 支持的导入文件格式
    IMPORT_FILE_FILTERS = [
        "Excel文件 (*.xlsx *.xls)",
        "CSV文件 (*.csv)",
        "JSON文件 (*.json)",
        "所有文件 (*.*)"
    ]
    
    # 支持的导出文件格式
    EXPORT_FILE_FILTERS = [
        "Excel文件 (*.xlsx)",
        "CSV文件 (*.csv)",
        "JSON文件 (*.json)",
        "PDF文件 (*.pdf)"
    ]
    
    # 备份文件扩展名
    BACKUP_FILE_EXTENSION = ".qjbackup"


class DatabaseConstants:
    """数据库相关常量"""
    
    # 表名
    TABLE_BILLS = "bills"
    TABLE_CATEGORIES = "categories"
    TABLE_ACCOUNTS = "accounts"
    TABLE_BUDGETS = "budgets"
    TABLE_SETTINGS = "settings"
    
    # 默认分类
    DEFAULT_EXPENSE_CATEGORIES = [
        ("餐饮", "🍽️"),
        ("交通", "🚗"),
        ("购物", "🛒"),
        ("娱乐", "🎮"),
        ("医疗", "🏥"),
        ("教育", "📚"),
        ("住房", "🏠"),
        ("通讯", "📱"),
        ("其他", "📝"),
    ]
    
    DEFAULT_INCOME_CATEGORIES = [
        ("工资", "💰"),
        ("奖金", "🎁"),
        ("投资", "📈"),
        ("兼职", "💼"),
        ("其他", "📝"),
    ]
    
    # 默认账户
    DEFAULT_ACCOUNTS = [
        ("现金", AccountType.CASH, "💵"),
        ("银行卡", AccountType.BANK_CARD, "💳"),
        ("支付宝", AccountType.BANK_CARD, "🅰️"),
        ("微信", AccountType.BANK_CARD, "💬"),
    ]


class MessageConstants:
    """消息常量"""
    
    # 成功消息
    MSG_SAVE_SUCCESS = "保存成功"
    MSG_DELETE_SUCCESS = "删除成功"
    MSG_IMPORT_SUCCESS = "导入成功"
    MSG_EXPORT_SUCCESS = "导出成功"
    
    # 错误消息
    MSG_SAVE_ERROR = "保存失败"
    MSG_DELETE_ERROR = "删除失败"
    MSG_IMPORT_ERROR = "导入失败"
    MSG_EXPORT_ERROR = "导出失败"
    MSG_VALIDATION_ERROR = "数据验证失败"
    MSG_DATABASE_ERROR = "数据库操作失败"
    
    # 确认消息
    MSG_CONFIRM_DELETE = "确定要删除吗？"
    MSG_CONFIRM_CLEAR = "确定要清空吗？"
    MSG_CONFIRM_OVERWRITE = "文件已存在，是否覆盖？"


class ChartConstants:
    """图表相关常量"""
    
    # 图表类型
    CHART_TYPE_PIE = "pie"
    CHART_TYPE_BAR = "bar"
    CHART_TYPE_LINE = "line"
    CHART_TYPE_AREA = "area"
    
    # 图表颜色
    CHART_COLORS = [
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
    ]
