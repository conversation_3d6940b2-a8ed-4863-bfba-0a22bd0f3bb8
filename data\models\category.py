"""
分类数据模型
"""

from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship

from data.models.bill import Base
from core.constants import CategoryType


class Category(Base):
    """分类模型"""
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)  # 分类名称
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)  # 父分类ID
    type = Column(Integer, nullable=False, default=CategoryType.EXPENSE)  # 分类类型
    icon = Column(String(50), nullable=True)  # 图标
    color = Column(String(20), nullable=True)  # 颜色
    sort_order = Column(Integer, nullable=False, default=0)  # 排序
    is_system = Column(Boolean, nullable=False, default=False)  # 是否系统分类
    is_deleted = Column(<PERSON>olean, nullable=False, default=False)  # 是否删除
    created_at = Column(DateTime, nullable=False, default=datetime.now)  # 创建时间
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)  # 更新时间
    
    # 关联关系
    parent = relationship("Category", remote_side=[id])
    children = relationship("Category", back_populates="parent")
    bills = relationship("Bill", back_populates="category")
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}', type={self.type})>"
    
    @property
    def type_name(self) -> str:
        """获取分类类型名称"""
        type_names = {
            CategoryType.EXPENSE: "支出",
            CategoryType.INCOME: "收入"
        }
        return type_names.get(self.type, "未知")
    
    @property
    def full_name(self) -> str:
        """获取完整分类名称（包含父分类）"""
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name
    
    @property
    def level(self) -> int:
        """获取分类层级"""
        if self.parent_id is None:
            return 1
        return 2
    
    def get_all_children(self) -> List["Category"]:
        """获取所有子分类（递归）"""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_all_children())
        return children
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "parent_id": self.parent_id,
            "type": self.type,
            "icon": self.icon,
            "color": self.color,
            "sort_order": self.sort_order,
            "is_system": self.is_system,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "Category":
        """从字典创建实例"""
        category = cls()
        category.name = data.get("name", "")
        category.parent_id = data.get("parent_id")
        category.type = data.get("type", CategoryType.EXPENSE)
        category.icon = data.get("icon")
        category.color = data.get("color")
        category.sort_order = data.get("sort_order", 0)
        category.is_system = data.get("is_system", False)
        category.is_deleted = data.get("is_deleted", False)
        
        # 处理日期时间
        if data.get("created_at"):
            if isinstance(data["created_at"], str):
                category.created_at = datetime.fromisoformat(data["created_at"])
            else:
                category.created_at = data["created_at"]
        
        if data.get("updated_at"):
            if isinstance(data["updated_at"], str):
                category.updated_at = datetime.fromisoformat(data["updated_at"])
            else:
                category.updated_at = data["updated_at"]
        
        return category
