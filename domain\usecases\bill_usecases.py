"""
账单用例
"""

from typing import List
from data.repositories.bill_repository import BillRepository
from data.repositories.account_repository import AccountRepository
from data.models.bill import Bill


class BillUseCases:
    """账单用例"""
    
    def __init__(self, bill_repository: BillRepository, account_repository: AccountRepository):
        self.bill_repository = bill_repository
        self.account_repository = account_repository
    
    def get_all_bills(self) -> List[Bill]:
        """获取所有账单"""
        return self.bill_repository.get_all()
