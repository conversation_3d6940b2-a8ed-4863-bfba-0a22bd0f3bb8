"""
记账输入界面
"""

from decimal import Decimal
from datetime import datetime, date
from typing import List, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QDateEdit, QButtonGroup, QFrame, QScrollArea,
    QMessageBox, QGroupBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont
from loguru import logger

from core.constants import BillType, CategoryType, UIConstants
from data.database.connection import DatabaseManager
from data.models.bill import Bill
from data.models.category import Category
from data.models.account import Account


class CalculatorWidget(QWidget):
    """计算器组件"""
    
    amount_changed = Signal(str)  # 金额变化信号
    
    def __init__(self):
        super().__init__()
        self.current_amount = "0"
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 显示屏
        self.display = QLineEdit()
        self.display.setReadOnly(True)
        self.display.setText("0.00")
        self.display.setAlignment(Qt.AlignRight)
        self.display.setStyleSheet("""
            QLineEdit {
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
            }
        """)
        layout.addWidget(self.display)
        
        # 按钮网格
        button_layout = QGridLayout()
        
        # 数字按钮
        buttons = [
            ('7', 0, 0), ('8', 0, 1), ('9', 0, 2), ('清空', 0, 3),
            ('4', 1, 0), ('5', 1, 1), ('6', 1, 2), ('+', 1, 3),
            ('1', 2, 0), ('2', 2, 1), ('3', 2, 2), ('=', 2, 3),
            ('0', 3, 0), ('.', 3, 1), ('删除', 3, 2), ('保存', 3, 3),
        ]
        
        for text, row, col in buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(50)
            btn.setStyleSheet(self._get_button_style(text))
            btn.clicked.connect(lambda checked, t=text: self._on_button_clicked(t))
            button_layout.addWidget(btn, row, col)
        
        layout.addLayout(button_layout)
    
    def _get_button_style(self, text: str) -> str:
        """获取按钮样式"""
        if text in ['清空', '删除']:
            return """
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """
        elif text in ['+', '=']:
            return """
                QPushButton {
                    background-color: #ff9800;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #f57c00;
                }
            """
        elif text == '保存':
            return """
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #388e3c;
                }
            """
        else:
            return """
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                }
            """
    
    def _on_button_clicked(self, text: str):
        """按钮点击处理"""
        if text.isdigit():
            self._input_digit(text)
        elif text == '.':
            self._input_decimal()
        elif text == '+':
            self._add_operation()
        elif text == '=':
            self._calculate()
        elif text == '清空':
            self._clear()
        elif text == '删除':
            self._backspace()
        elif text == '保存':
            self._save()
    
    def _input_digit(self, digit: str):
        """输入数字"""
        if self.current_amount == "0":
            self.current_amount = digit
        else:
            self.current_amount += digit
        self._update_display()
    
    def _input_decimal(self):
        """输入小数点"""
        if '.' not in self.current_amount:
            self.current_amount += '.'
            self._update_display()
    
    def _add_operation(self):
        """加法操作（简化实现）"""
        # 这里可以实现更复杂的计算逻辑
        pass
    
    def _calculate(self):
        """计算结果"""
        # 简化实现，直接显示当前金额
        self._update_display()
    
    def _clear(self):
        """清空"""
        self.current_amount = "0"
        self._update_display()
    
    def _backspace(self):
        """删除最后一位"""
        if len(self.current_amount) > 1:
            self.current_amount = self.current_amount[:-1]
        else:
            self.current_amount = "0"
        self._update_display()
    
    def _save(self):
        """保存金额"""
        self.amount_changed.emit(self.current_amount)
    
    def _update_display(self):
        """更新显示"""
        try:
            amount = float(self.current_amount) if self.current_amount != "" else 0
            self.display.setText(f"{amount:.2f}")
        except ValueError:
            self.display.setText("0.00")
    
    def get_amount(self) -> Decimal:
        """获取当前金额"""
        try:
            return Decimal(self.current_amount)
        except:
            return Decimal('0.00')


class BillInputView(QWidget):
    """记账输入界面"""
    
    bill_saved = Signal(Bill)  # 账单保存信号
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.categories: List[Category] = []
        self.accounts: List[Account] = []
        self.current_bill_type = BillType.EXPENSE
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        
        # 左侧：分类和表单
        left_widget = self._create_left_panel()
        layout.addWidget(left_widget, 2)
        
        # 右侧：计算器
        right_widget = self._create_right_panel()
        layout.addWidget(right_widget, 1)
    
    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 收支类型选择
        type_group = QGroupBox("收支类型")
        type_layout = QHBoxLayout(type_group)
        
        self.type_button_group = QButtonGroup()
        
        expense_btn = QPushButton("支出")
        expense_btn.setCheckable(True)
        expense_btn.setChecked(True)
        expense_btn.clicked.connect(lambda: self._set_bill_type(BillType.EXPENSE))
        self.type_button_group.addButton(expense_btn, BillType.EXPENSE)
        type_layout.addWidget(expense_btn)
        
        income_btn = QPushButton("收入")
        income_btn.setCheckable(True)
        income_btn.clicked.connect(lambda: self._set_bill_type(BillType.INCOME))
        self.type_button_group.addButton(income_btn, BillType.INCOME)
        type_layout.addWidget(income_btn)
        
        layout.addWidget(type_group)
        
        # 分类选择
        category_group = QGroupBox("选择分类")
        self.category_scroll = QScrollArea()
        self.category_widget = QWidget()
        self.category_layout = QGridLayout(self.category_widget)
        self.category_scroll.setWidget(self.category_widget)
        self.category_scroll.setWidgetResizable(True)
        self.category_scroll.setMaximumHeight(200)
        
        category_layout = QVBoxLayout(category_group)
        category_layout.addWidget(self.category_scroll)
        layout.addWidget(category_group)
        
        # 表单区域
        form_group = QGroupBox("账单信息")
        form_layout = QVBoxLayout(form_group)
        
        # 账户选择
        account_layout = QHBoxLayout()
        account_layout.addWidget(QLabel("账户:"))
        self.account_combo = QComboBox()
        account_layout.addWidget(self.account_combo)
        form_layout.addLayout(account_layout)
        
        # 日期选择
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("日期:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        date_layout.addWidget(self.date_edit)
        form_layout.addLayout(date_layout)
        
        # 备注
        form_layout.addWidget(QLabel("备注:"))
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        form_layout.addWidget(self.description_edit)
        
        layout.addWidget(form_group)
        
        return widget
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 计算器
        self.calculator = CalculatorWidget()
        self.calculator.amount_changed.connect(self._on_amount_changed)
        layout.addWidget(self.calculator)
        
        return widget
    
    def _set_bill_type(self, bill_type: BillType):
        """设置账单类型"""
        self.current_bill_type = bill_type
        self._update_categories()
    
    def _update_categories(self):
        """更新分类显示"""
        # 清空现有分类按钮
        for i in reversed(range(self.category_layout.count())):
            self.category_layout.itemAt(i).widget().setParent(None)
        
        # 根据当前类型筛选分类
        category_type = CategoryType.EXPENSE if self.current_bill_type == BillType.EXPENSE else CategoryType.INCOME
        filtered_categories = [cat for cat in self.categories if cat.type == category_type]
        
        # 创建分类按钮
        row, col = 0, 0
        for category in filtered_categories:
            btn = QPushButton(f"{category.icon}\n{category.name}")
            btn.setMinimumHeight(60)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, cat=category: self._select_category(cat))
            self.category_layout.addWidget(btn, row, col)
            
            col += 1
            if col >= 3:  # 每行3个按钮
                col = 0
                row += 1
    
    def _select_category(self, category: Category):
        """选择分类"""
        self.selected_category = category
        # 取消其他按钮的选中状态
        for i in range(self.category_layout.count()):
            widget = self.category_layout.itemAt(i).widget()
            if widget != self.sender():
                widget.setChecked(False)
    
    def _on_amount_changed(self, amount_str: str):
        """金额变化处理"""
        self._save_bill()
    
    def _save_bill(self):
        """保存账单"""
        try:
            # 验证数据
            amount = self.calculator.get_amount()
            if amount <= 0:
                QMessageBox.warning(self, "警告", "请输入有效金额")
                return
            
            if not hasattr(self, 'selected_category'):
                QMessageBox.warning(self, "警告", "请选择分类")
                return
            
            if self.account_combo.currentIndex() < 0:
                QMessageBox.warning(self, "警告", "请选择账户")
                return
            
            # 创建账单
            bill = Bill()
            bill.type = self.current_bill_type
            bill.amount = amount
            bill.category_id = self.selected_category.id
            bill.account_id = self.accounts[self.account_combo.currentIndex()].id
            bill.description = self.description_edit.toPlainText().strip()
            bill.bill_date = self.date_edit.date().toPython()
            
            # 保存到数据库
            with self.db_manager.get_session() as session:
                session.add(bill)
                session.commit()
                session.refresh(bill)
            
            # 发送信号
            self.bill_saved.emit(bill)
            
            # 显示成功消息
            QMessageBox.information(self, "成功", "账单保存成功！")
            
            # 清空表单
            self._clear_form()
            
            logger.info(f"账单保存成功: {bill}")
            
        except Exception as e:
            logger.error(f"保存账单失败: {e}")
            QMessageBox.critical(self, "错误", f"保存账单失败: {e}")
    
    def _clear_form(self):
        """清空表单"""
        self.calculator._clear()
        self.description_edit.clear()
        self.date_edit.setDate(QDate.currentDate())
        
        # 取消分类选择
        for i in range(self.category_layout.count()):
            widget = self.category_layout.itemAt(i).widget()
            widget.setChecked(False)
        
        if hasattr(self, 'selected_category'):
            delattr(self, 'selected_category')
    
    def load_data(self):
        """加载数据"""
        try:
            with self.db_manager.get_session() as session:
                # 加载分类
                self.categories = session.query(Category).filter(
                    Category.is_deleted == False
                ).order_by(Category.sort_order, Category.name).all()
                
                # 加载账户
                self.accounts = session.query(Account).filter(
                    Account.is_deleted == False,
                    Account.is_hidden == False
                ).order_by(Account.sort_order, Account.name).all()
                
                # 更新UI
                self._update_categories()
                self._update_accounts()
                
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载数据失败: {e}")
    
    def _update_accounts(self):
        """更新账户下拉框"""
        self.account_combo.clear()
        for account in self.accounts:
            self.account_combo.addItem(f"{account.name} ({account.formatted_balance})")
