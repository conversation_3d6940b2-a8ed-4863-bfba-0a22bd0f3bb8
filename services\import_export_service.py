"""
导入导出服务
"""

from data.repositories.bill_repository import BillRepository
from data.repositories.category_repository import CategoryRepository
from data.repositories.account_repository import AccountRepository


class ImportExportService:
    """导入导出服务"""
    
    def __init__(self, bill_repository: BillRepository, 
                 category_repository: CategoryRepository,
                 account_repository: AccountRepository):
        self.bill_repository = bill_repository
        self.category_repository = category_repository
        self.account_repository = account_repository
