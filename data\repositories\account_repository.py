"""
账户仓库实现
"""

from typing import List, Optional
from sqlalchemy.orm import sessionmaker

from data.models.account import Account
from core.exceptions import DatabaseException


class AccountRepository:
    """账户仓库"""
    
    def __init__(self, session_factory: sessionmaker):
        self.session_factory = session_factory
    
    def get_all(self) -> List[Account]:
        """获取所有账户"""
        with self.session_factory() as session:
            return session.query(Account).filter(Account.is_deleted == False).all()
