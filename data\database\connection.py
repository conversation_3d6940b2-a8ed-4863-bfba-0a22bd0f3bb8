"""
数据库连接管理
"""

from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from loguru import logger

from data.models.bill import Base
from data.models.category import Category
from data.models.account import Account
from core.constants import CategoryType, AccountType, DatabaseConstants
from core.utils import SingletonMeta


def _enable_foreign_keys(dbapi_connection, connection_record):
    """启用SQLite外键约束"""
    cursor = dbapi_connection.cursor()
    cursor.execute("PRAGMA foreign_keys=ON")
    cursor.close()


class DatabaseManager(metaclass=SingletonMeta):
    """数据库管理器"""
    
    def __init__(self, database_url: str = None, echo: bool = False):
        self.database_url = database_url or "sqlite:///qianji_mini.db"
        self.echo = echo
        self.engine = None
        self.session_factory = None
        self._initialize()
    
    def _initialize(self):
        """初始化数据库连接"""
        try:
            self.engine = create_engine(
                self.database_url,
                echo=self.echo,
                pool_pre_ping=True,
                connect_args={"check_same_thread": False} if "sqlite" in self.database_url else {}
            )
            
            # 为SQLite启用外键约束
            if "sqlite" in self.database_url:
                event.listen(Engine, "connect", _enable_foreign_keys)
            
            self.session_factory = sessionmaker(bind=self.engine)
            logger.info(f"数据库连接已建立: {self.database_url}")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def create_tables(self):
        """创建数据库表"""
        try:
            Base.metadata.create_all(self.engine)
            logger.info("数据库表创建成功")
            
            # 初始化默认数据
            self._initialize_default_data()
            
        except Exception as e:
            logger.error(f"数据库表创建失败: {e}")
            raise
    
    def drop_tables(self):
        """删除数据库表"""
        try:
            Base.metadata.drop_all(self.engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"数据库表删除失败: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """获取数据库会话（上下文管理器）"""
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def _initialize_default_data(self):
        """初始化默认数据"""
        with self.get_session() as session:
            # 检查是否已有数据
            if session.query(Category).count() > 0:
                return
            
            # 创建默认支出分类
            for name, icon in DatabaseConstants.DEFAULT_EXPENSE_CATEGORIES:
                category = Category(
                    name=name,
                    type=CategoryType.EXPENSE,
                    icon=icon,
                    is_system=True
                )
                session.add(category)
            
            # 创建默认收入分类
            for name, icon in DatabaseConstants.DEFAULT_INCOME_CATEGORIES:
                category = Category(
                    name=name,
                    type=CategoryType.INCOME,
                    icon=icon,
                    is_system=True
                )
                session.add(category)
            
            # 创建默认账户
            for name, account_type, icon in DatabaseConstants.DEFAULT_ACCOUNTS:
                account = Account(
                    name=name,
                    type=account_type,
                    icon=icon,
                    balance=0
                )
                session.add(account)
            
            session.commit()
            logger.info("默认数据初始化完成")
    
    def backup_database(self, backup_path: str):
        """备份数据库"""
        try:
            if "sqlite" in self.database_url:
                import shutil
                db_path = self.database_url.replace("sqlite:///", "")
                shutil.copy2(db_path, backup_path)
                logger.info(f"数据库备份成功: {backup_path}")
            else:
                logger.warning("当前数据库类型不支持文件备份")
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise
    
    def restore_database(self, backup_path: str):
        """恢复数据库"""
        try:
            if "sqlite" in self.database_url:
                import shutil
                db_path = self.database_url.replace("sqlite:///", "")
                shutil.copy2(backup_path, db_path)
                logger.info(f"数据库恢复成功: {backup_path}")
                
                # 重新初始化连接
                self._initialize()
            else:
                logger.warning("当前数据库类型不支持文件恢复")
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            raise
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            with self.get_session() as session:
                from data.models.bill import Bill
                
                info = {
                    "database_url": self.database_url,
                    "bills_count": session.query(Bill).filter(Bill.is_deleted == False).count(),
                    "categories_count": session.query(Category).filter(Category.is_deleted == False).count(),
                    "accounts_count": session.query(Account).filter(Account.is_deleted == False).count(),
                }
                
                # 获取数据库文件大小（仅SQLite）
                if "sqlite" in self.database_url:
                    import os
                    db_path = self.database_url.replace("sqlite:///", "")
                    if os.path.exists(db_path):
                        info["database_size"] = os.path.getsize(db_path)
                
                return info
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {}
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")
