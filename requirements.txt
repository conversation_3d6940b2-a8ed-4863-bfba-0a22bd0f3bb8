# 钱迹Mini - Python依赖包列表

# 核心GUI框架
PySide6>=6.6.0
PySide6-Addons>=6.6.0
PySide6-Essentials>=6.6.0

# 数据库相关
SQLAlchemy>=2.0.0
alembic>=1.12.0
sqlite3  # Python内置

# 数据验证和序列化
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 依赖注入
dependency-injector>=4.41.0

# 图表和可视化
matplotlib>=3.8.0
plotly>=5.17.0
pyqtgraph>=0.13.3

# 文件处理
openpyxl>=3.1.0
pandas>=2.1.0
xlsxwriter>=3.1.0

# 日期时间处理
python-dateutil>=2.8.0
pytz>=2023.3

# 加密和安全
cryptography>=41.0.0
bcrypt>=4.1.0

# 网络请求
requests>=2.31.0
httpx>=0.25.0

# 配置管理
python-dotenv>=1.0.0
configparser  # Python内置

# 日志记录
loguru>=0.7.0

# 测试框架
pytest>=7.4.0
pytest-qt>=4.2.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# 代码质量
black>=23.0.0
flake8>=6.1.0
mypy>=1.7.0
isort>=5.12.0

# 打包和分发
PyInstaller>=6.2.0
cx-Freeze>=6.15.0

# 开发工具
pre-commit>=3.5.0
autopep8>=2.0.0

# 可选：Android支持
# kivy>=2.2.0
# buildozer>=1.5.0
# python-for-android>=2023.0.0

# 可选：跨平台支持
# briefcase>=0.3.17
# toga>=0.4.0
