"""
账户用例
"""

from typing import List
from data.repositories.account_repository import AccountRepository
from data.models.account import Account


class AccountUseCases:
    """账户用例"""
    
    def __init__(self, account_repository: AccountRepository):
        self.account_repository = account_repository
    
    def get_all_accounts(self) -> List[Account]:
        """获取所有账户"""
        return self.account_repository.get_all()
