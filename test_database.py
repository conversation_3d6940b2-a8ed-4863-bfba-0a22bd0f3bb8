#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库初始化
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.database.connection import DatabaseManager
from data.models.category import Category
from data.models.account import Account


def test_database():
    """测试数据库功能"""
    print("开始测试数据库...")
    
    # 创建数据库管理器
    db_manager = DatabaseManager()
    
    # 获取数据库信息
    info = db_manager.get_database_info()
    print(f"数据库信息: {info}")
    
    # 测试查询分类
    with db_manager.get_session() as session:
        categories = session.query(Category).all()
        print(f"\n分类数量: {len(categories)}")
        print("分类列表:")
        for category in categories:
            print(f"  - {category.name} ({category.type_name}) {category.icon}")
    
    # 测试查询账户
    with db_manager.get_session() as session:
        accounts = session.query(Account).all()
        print(f"\n账户数量: {len(accounts)}")
        print("账户列表:")
        for account in accounts:
            print(f"  - {account.name} ({account.type_name}) {account.formatted_balance}")
    
    print("\n数据库测试完成！")


if __name__ == "__main__":
    test_database()
