"""
账户数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, Numeric, Boolean
from sqlalchemy.orm import relationship

from data.models.bill import Base
from core.constants import AccountType, Currency


class Account(Base):
    """账户模型"""
    __tablename__ = "accounts"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)  # 账户名称
    type = Column(Integer, nullable=False, default=AccountType.CASH)  # 账户类型
    balance = Column(Numeric(precision=15, scale=2), nullable=False, default=0)  # 余额
    currency = Column(String(10), nullable=False, default=Currency.CNY)  # 货币类型
    icon = Column(String(50), nullable=True)  # 图标
    color = Column(String(20), nullable=True)  # 颜色
    description = Column(String(200), nullable=True)  # 描述
    is_hidden = Column(Boolean, nullable=False, default=False)  # 是否隐藏
    is_credit_card = Column(Boolean, nullable=False, default=False)  # 是否信用卡
    credit_limit = Column(Numeric(precision=15, scale=2), nullable=True)  # 信用额度
    bill_day = Column(Integer, nullable=True)  # 账单日
    due_day = Column(Integer, nullable=True)  # 还款日
    sort_order = Column(Integer, nullable=False, default=0)  # 排序
    is_deleted = Column(Boolean, nullable=False, default=False)  # 是否删除
    created_at = Column(DateTime, nullable=False, default=datetime.now)  # 创建时间
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)  # 更新时间
    
    # 关联关系
    bills = relationship("Bill", foreign_keys="Bill.account_id", back_populates="account")
    transfer_bills = relationship("Bill", foreign_keys="Bill.to_account_id", back_populates="to_account")
    
    def __repr__(self):
        return f"<Account(id={self.id}, name='{self.name}', type={self.type}, balance={self.balance})>"
    
    @property
    def type_name(self) -> str:
        """获取账户类型名称"""
        type_names = {
            AccountType.CASH: "现金",
            AccountType.BANK_CARD: "银行卡",
            AccountType.CREDIT_CARD: "信用卡",
            AccountType.RECHARGE_CARD: "充值卡",
            AccountType.INVESTMENT: "投资理财"
        }
        return type_names.get(self.type, "未知")
    
    @property
    def formatted_balance(self) -> str:
        """格式化余额显示"""
        from core.utils import format_currency
        return format_currency(self.balance, self.currency)
    
    @property
    def available_balance(self) -> Decimal:
        """可用余额（信用卡考虑信用额度）"""
        if self.is_credit_card and self.credit_limit:
            return self.credit_limit + self.balance  # 信用卡余额为负数
        return self.balance
    
    @property
    def formatted_available_balance(self) -> str:
        """格式化可用余额显示"""
        from core.utils import format_currency
        return format_currency(self.available_balance, self.currency)
    
    def update_balance(self, amount: Decimal, operation: str = "add") -> None:
        """更新账户余额"""
        if operation == "add":
            self.balance += amount
        elif operation == "subtract":
            self.balance -= amount
        elif operation == "set":
            self.balance = amount
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "balance": float(self.balance),
            "currency": self.currency,
            "icon": self.icon,
            "color": self.color,
            "description": self.description,
            "is_hidden": self.is_hidden,
            "is_credit_card": self.is_credit_card,
            "credit_limit": float(self.credit_limit) if self.credit_limit else None,
            "bill_day": self.bill_day,
            "due_day": self.due_day,
            "sort_order": self.sort_order,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "Account":
        """从字典创建实例"""
        account = cls()
        account.name = data.get("name", "")
        account.type = data.get("type", AccountType.CASH)
        account.balance = Decimal(str(data.get("balance", 0)))
        account.currency = data.get("currency", Currency.CNY)
        account.icon = data.get("icon")
        account.color = data.get("color")
        account.description = data.get("description")
        account.is_hidden = data.get("is_hidden", False)
        account.is_credit_card = data.get("is_credit_card", False)
        
        if data.get("credit_limit") is not None:
            account.credit_limit = Decimal(str(data["credit_limit"]))
        
        account.bill_day = data.get("bill_day")
        account.due_day = data.get("due_day")
        account.sort_order = data.get("sort_order", 0)
        account.is_deleted = data.get("is_deleted", False)
        
        # 处理日期时间
        if data.get("created_at"):
            if isinstance(data["created_at"], str):
                account.created_at = datetime.fromisoformat(data["created_at"])
            else:
                account.created_at = data["created_at"]
        
        if data.get("updated_at"):
            if isinstance(data["updated_at"], str):
                account.updated_at = datetime.fromisoformat(data["updated_at"])
            else:
                account.updated_at = data["updated_at"]
        
        return account
