# 钱迹Mini - 技术架构设计 (PySide6版本)

## 1. 整体架构

### 1.1 架构模式
采用**MVP (Model-View-Presenter)** 架构模式，结合**Clean Architecture**原则：

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │    Views    │  │   Widgets   │  │ Presenters  │     │
│  │  (QWidget)  │  │ (QWidget)   │  │  (Python)   │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    Business Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  Use Cases  │  │ Repositories│  │   Services  │     │
│  │  (Python)   │  │  (Python)   │  │  (Python)   │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                      Data Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │   SQLite    │  │ File System │  │ Cloud APIs  │     │
│  │  (Python)   │  │  (Python)   │  │  (Python)   │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

#### 核心框架
- **PySide6**: Qt6的Python绑定，跨平台GUI框架
- **Python 3.9+**: 编程语言

#### UI框架
- **Qt6**: 成熟的跨平台UI框架
- **QML**: 声明式UI语言（可选）

#### 数据存储
- **SQLite**: 本地关系型数据库
- **SQLAlchemy**: Python ORM框架
- **Pydantic**: 数据验证和序列化

#### 依赖注入
- **dependency-injector**: Python依赖注入框架

## 2. 项目结构

### 2.1 目录结构
```
qianji_mini/
├── main.py                        # 应用入口
├── requirements.txt               # 依赖包列表
├── config/
│   ├── __init__.py
│   ├── settings.py               # 应用配置
│   └── database.py               # 数据库配置
├── core/
│   ├── __init__.py
│   ├── constants.py              # 常量定义
│   ├── exceptions.py             # 异常处理
│   ├── utils.py                  # 工具函数
│   └── container.py              # 依赖注入容器
├── data/
│   ├── __init__.py
│   ├── models/                   # SQLAlchemy模型
│   │   ├── __init__.py
│   │   ├── bill.py
│   │   ├── category.py
│   │   └── account.py
│   ├── repositories/             # 仓库实现
│   │   ├── __init__.py
│   │   ├── bill_repository.py
│   │   ├── category_repository.py
│   │   └── account_repository.py
│   └── database/                 # 数据库相关
│       ├── __init__.py
│       ├── connection.py
│       └── migrations/
├── domain/
│   ├── __init__.py
│   ├── entities/                 # 业务实体
│   │   ├── __init__.py
│   │   ├── bill.py
│   │   ├── category.py
│   │   └── account.py
│   ├── repositories/             # 仓库接口
│   │   ├── __init__.py
│   │   └── interfaces.py
│   └── usecases/                 # 用例
│       ├── __init__.py
│       ├── bill_usecases.py
│       └── account_usecases.py
├── presentation/
│   ├── __init__.py
│   ├── views/                    # 主要视图
│   │   ├── __init__.py
│   │   ├── main_window.py
│   │   ├── bill_input_view.py
│   │   ├── bill_list_view.py
│   │   ├── statistics_view.py
│   │   └── settings_view.py
│   ├── widgets/                  # 自定义组件
│   │   ├── __init__.py
│   │   ├── calculator_widget.py
│   │   ├── category_selector.py
│   │   └── chart_widget.py
│   ├── presenters/               # 表现层逻辑
│   │   ├── __init__.py
│   │   ├── bill_presenter.py
│   │   └── statistics_presenter.py
│   └── resources/                # 资源文件
│       ├── icons/
│       ├── styles/
│       └── qml/                  # QML文件(可选)
├── services/
│   ├── __init__.py
│   ├── import_export_service.py  # 导入导出服务
│   ├── sync_service.py           # 同步服务
│   └── backup_service.py         # 备份服务
└── tests/
    ├── __init__.py
    ├── unit/
    ├── integration/
    └── fixtures/
```

### 2.2 核心模块划分

#### 2.2.1 记账模块 (Billing)
- **数据层**: SQLAlchemy模型，仓库实现
- **业务层**: 记账用例，业务逻辑
- **表现层**: 记账界面，计算器组件

#### 2.2.2 资产模块 (Assets)
- **数据层**: 账户模型，资产统计
- **业务层**: 资产管理用例
- **表现层**: 资产管理界面

#### 2.2.3 统计模块 (Statistics)
- **数据层**: 统计查询，数据聚合
- **业务层**: 统计分析用例
- **表现层**: 图表展示，报表生成

### 2.3 跨平台支持策略

#### 2.3.1 Windows支持
- **主要平台**: 使用PySide6原生支持
- **打包方式**: PyInstaller或cx_Freeze
- **系统集成**: Windows通知，文件关联

#### 2.3.2 Android支持方案
**方案一**: 使用Kivy + Buildozer
```python
# buildozer.spec配置
[app]
title = 钱迹Mini
package.name = qianjimini
package.domain = com.qianji.mini

[buildozer]
log_level = 2
```

**方案二**: 使用BeeWare + Toga
```python
# pyproject.toml配置
[tool.briefcase.app.qianjimini.android]
requires = ["toga-android"]
```

**推荐方案**: 优先开发Windows版本，后期考虑使用Kivy移植到Android

## 3. 数据层设计

### 3.1 数据库设计

#### 3.1.1 Drift配置
```dart
// database.dart
@DriftDatabase(tables: [Bills, Categories, Accounts, Budgets])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
    },
  );
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'qianji_mini.db'));
    return NativeDatabase.createInBackground(file);
  });
}
```

#### 3.1.2 表定义
```dart
// tables.dart
@DataClassName('Bill')
class Bills extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get type => integer()(); // 0:支出 1:收入 2:转账
  RealColumn get amount => real()();
  IntColumn get categoryId => integer().nullable()();
  IntColumn get accountId => integer().nullable()();
  IntColumn get toAccountId => integer().nullable()();
  TextColumn get description => text().nullable()();
  TextColumn get imagePath => text().nullable()();
  DateTimeColumn get billDate => dateTime()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();
}

@DataClassName('Category')
class Categories extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text()();
  IntColumn get parentId => integer().nullable()();
  IntColumn get type => integer()(); // 0:支出 1:收入
  TextColumn get icon => text().nullable()();
  TextColumn get color => text().nullable()();
  IntColumn get sortOrder => integer().withDefault(const Constant(0))();
}
```

### 3.2 仓库模式实现

#### 3.2.1 仓库接口
```dart
// domain/repositories/bill_repository.dart
abstract class BillRepository {
  Future<List<Bill>> getBills({
    DateTime? startDate,
    DateTime? endDate,
    int? categoryId,
    int? accountId,
  });
  
  Future<Bill> createBill(Bill bill);
  Future<Bill> updateBill(Bill bill);
  Future<void> deleteBill(int id);
  
  Stream<List<Bill>> watchBills();
}
```

#### 3.2.2 仓库实现
```dart
// data/repositories/bill_repository_impl.dart
@Injectable(as: BillRepository)
class BillRepositoryImpl implements BillRepository {
  final BillLocalDataSource _localDataSource;
  
  BillRepositoryImpl(this._localDataSource);
  
  @override
  Future<List<Bill>> getBills({
    DateTime? startDate,
    DateTime? endDate,
    int? categoryId,
    int? accountId,
  }) async {
    final billModels = await _localDataSource.getBills(
      startDate: startDate,
      endDate: endDate,
      categoryId: categoryId,
      accountId: accountId,
    );
    return billModels.map((model) => model.toEntity()).toList();
  }
  
  // 其他方法实现...
}
```

## 4. 业务层设计

### 4.1 用例模式

#### 4.1.1 用例基类
```dart
// core/usecases/usecase.dart
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

abstract class StreamUseCase<Type, Params> {
  Stream<Either<Failure, Type>> call(Params params);
}

class NoParams extends Equatable {
  @override
  List<Object> get props => [];
}
```

#### 4.1.2 具体用例实现
```dart
// domain/usecases/create_bill.dart
@injectable
class CreateBill implements UseCase<Bill, CreateBillParams> {
  final BillRepository _repository;
  
  CreateBill(this._repository);
  
  @override
  Future<Either<Failure, Bill>> call(CreateBillParams params) async {
    try {
      final bill = await _repository.createBill(params.bill);
      return Right(bill);
    } catch (e) {
      return Left(DatabaseFailure(e.toString()));
    }
  }
}

class CreateBillParams extends Equatable {
  final Bill bill;
  
  const CreateBillParams({required this.bill});
  
  @override
  List<Object> get props => [bill];
}
```

## 5. 表现层设计

### 5.1 状态管理 (Riverpod)

#### 5.1.1 Provider定义
```dart
// presentation/providers/bill_provider.dart
@riverpod
class BillNotifier extends _$BillNotifier {
  @override
  FutureOr<List<Bill>> build() async {
    final getBills = ref.read(getBillsUseCaseProvider);
    final result = await getBills(NoParams());
    return result.fold(
      (failure) => throw failure,
      (bills) => bills,
    );
  }
  
  Future<void> createBill(Bill bill) async {
    state = const AsyncLoading();
    final createBill = ref.read(createBillUseCaseProvider);
    final result = await createBill(CreateBillParams(bill: bill));
    
    result.fold(
      (failure) => state = AsyncError(failure, StackTrace.current),
      (newBill) {
        final currentBills = state.value ?? [];
        state = AsyncData([...currentBills, newBill]);
      },
    );
  }
}

@riverpod
BillNotifier billNotifier(BillNotifierRef ref) => BillNotifier();
```

#### 5.1.2 UI状态消费
```dart
// presentation/pages/bill_list_page.dart
class BillListPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final billsAsync = ref.watch(billNotifierProvider);
    
    return Scaffold(
      body: billsAsync.when(
        data: (bills) => BillListView(bills: bills),
        loading: () => const CircularProgressIndicator(),
        error: (error, stack) => ErrorWidget(error.toString()),
      ),
    );
  }
}
```

### 5.2 路由管理

#### 5.2.1 Go Router配置
```dart
// app/router/app_router.dart
@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    AutoRoute(
      page: MainWrapperRoute.page,
      path: '/',
      initial: true,
      children: [
        AutoRoute(page: HomeRoute.page, path: '/home'),
        AutoRoute(page: BillListRoute.page, path: '/bills'),
        AutoRoute(page: StatisticsRoute.page, path: '/statistics'),
        AutoRoute(page: AssetsRoute.page, path: '/assets'),
        AutoRoute(page: SettingsRoute.page, path: '/settings'),
      ],
    ),
    AutoRoute(page: BillInputRoute.page, path: '/bill-input'),
    AutoRoute(page: BillDetailRoute.page, path: '/bill-detail/:id'),
  ];
}
```

## 6. 依赖注入配置

### 6.1 GetIt + Injectable配置
```dart
// core/di/injection.dart
@InjectableInit()
Future<void> configureDependencies() async => getIt.init();

final getIt = GetIt.instance;

@module
abstract class RegisterModule {
  @singleton
  AppDatabase get database => AppDatabase();
  
  @singleton
  SharedPreferences get prefs => throw UnimplementedError();
}
```

### 6.2 主函数配置
```dart
// main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化SharedPreferences
  final prefs = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(prefs);
  
  // 配置依赖注入
  await configureDependencies();
  
  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}
```

## 7. 错误处理

### 7.1 错误类型定义
```dart
// core/errors/failures.dart
abstract class Failure extends Equatable {
  final String message;
  
  const Failure(this.message);
  
  @override
  List<Object> get props => [message];
}

class DatabaseFailure extends Failure {
  const DatabaseFailure(String message) : super(message);
}

class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message);
}

class ValidationFailure extends Failure {
  const ValidationFailure(String message) : super(message);
}
```

### 7.2 全局错误处理
```dart
// core/errors/error_handler.dart
class ErrorHandler {
  static String getErrorMessage(Failure failure) {
    switch (failure.runtimeType) {
      case DatabaseFailure:
        return '数据库操作失败：${failure.message}';
      case NetworkFailure:
        return '网络连接失败：${failure.message}';
      case ValidationFailure:
        return '数据验证失败：${failure.message}';
      default:
        return '未知错误：${failure.message}';
    }
  }
}
```

## 8. 测试策略

### 8.1 测试结构
```
test/
├── unit/
│   ├── domain/
│   ├── data/
│   └── presentation/
├── integration/
└── widget/
```

### 8.2 测试配置
```dart
// test/helpers/test_helper.dart
@GenerateMocks([
  BillRepository,
  BillLocalDataSource,
  AppDatabase,
])
void main() {}
```

这个技术架构设计为钱迹Mini提供了一个可扩展、可维护的代码结构，确保项目能够高质量地实现所有需求功能。
