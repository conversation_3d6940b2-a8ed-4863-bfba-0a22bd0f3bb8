"""
依赖注入容器
"""

from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

from config.settings import Settings
from data.database.connection import DatabaseManager
from data.repositories.bill_repository import BillRepository
from data.repositories.category_repository import CategoryRepository
from data.repositories.account_repository import AccountRepository
from domain.usecases.bill_usecases import BillUseCases
from domain.usecases.account_usecases import AccountUseCases
from services.import_export_service import ImportExportService
from services.sync_service import SyncService
from services.backup_service import BackupService


class Container(containers.DeclarativeContainer):
    """依赖注入容器"""
    
    # 配置
    config = providers.Configuration()
    
    # 数据库
    database_manager = providers.Singleton(
        DatabaseManager,
        database_url=config.database.url,
        echo=config.database.echo
    )
    
    # 仓库层
    bill_repository = providers.Factory(
        BillRepository,
        session_factory=database_manager.provided.session_factory
    )
    
    category_repository = providers.Factory(
        CategoryRepository,
        session_factory=database_manager.provided.session_factory
    )
    
    account_repository = providers.Factory(
        AccountRepository,
        session_factory=database_manager.provided.session_factory
    )
    
    # 用例层
    bill_usecases = providers.Factory(
        BillUseCases,
        bill_repository=bill_repository,
        account_repository=account_repository
    )
    
    account_usecases = providers.Factory(
        AccountUseCases,
        account_repository=account_repository
    )
    
    # 服务层
    import_export_service = providers.Factory(
        ImportExportService,
        bill_repository=bill_repository,
        category_repository=category_repository,
        account_repository=account_repository
    )
    
    sync_service = providers.Factory(
        SyncService,
        import_export_service=import_export_service
    )
    
    backup_service = providers.Factory(
        BackupService,
        database_manager=database_manager,
        import_export_service=import_export_service
    )
