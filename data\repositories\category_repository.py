"""
分类仓库实现
"""

from typing import List, Optional
from sqlalchemy.orm import sessionmaker

from data.models.category import Category
from core.exceptions import DatabaseException


class CategoryRepository:
    """分类仓库"""
    
    def __init__(self, session_factory: sessionmaker):
        self.session_factory = session_factory
    
    def get_all(self) -> List[Category]:
        """获取所有分类"""
        with self.session_factory() as session:
            return session.query(Category).filter(Category.is_deleted == False).all()
