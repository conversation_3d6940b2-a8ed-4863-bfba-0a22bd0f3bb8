"""
账单数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, Numeric, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship, declarative_base

from core.constants import BillType

Base = declarative_base()


class Bill(Base):
    """账单模型"""
    __tablename__ = "bills"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(Integer, nullable=False, default=BillType.EXPENSE)  # 账单类型
    amount = Column(Numeric(precision=15, scale=2), nullable=False)  # 金额
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)  # 分类ID
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)  # 账户ID
    to_account_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)  # 转账目标账户ID
    description = Column(Text, nullable=True)  # 描述/备注
    image_path = Column(String(500), nullable=True)  # 图片路径
    tags = Column(String(200), nullable=True)  # 标签（逗号分隔）
    bill_date = Column(DateTime, nullable=False, default=datetime.now)  # 账单日期
    created_at = Column(DateTime, nullable=False, default=datetime.now)  # 创建时间
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)  # 更新时间
    is_deleted = Column(Boolean, nullable=False, default=False)  # 是否删除
    
    # 关联关系
    category = relationship("Category", foreign_keys=[category_id])
    account = relationship("Account", foreign_keys=[account_id])
    to_account = relationship("Account", foreign_keys=[to_account_id])
    
    def __repr__(self):
        return f"<Bill(id={self.id}, type={self.type}, amount={self.amount}, description='{self.description}')>"
    
    @property
    def type_name(self) -> str:
        """获取账单类型名称"""
        type_names = {
            BillType.EXPENSE: "支出",
            BillType.INCOME: "收入",
            BillType.TRANSFER: "转账"
        }
        return type_names.get(self.type, "未知")
    
    @property
    def formatted_amount(self) -> str:
        """格式化金额显示"""
        from core.utils import format_currency
        return format_currency(self.amount)
    
    @property
    def tag_list(self) -> list:
        """获取标签列表"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(",") if tag.strip()]
        return []
    
    @tag_list.setter
    def tag_list(self, tags: list):
        """设置标签列表"""
        self.tags = ",".join(tags) if tags else None
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "type": self.type,
            "amount": float(self.amount),
            "category_id": self.category_id,
            "account_id": self.account_id,
            "to_account_id": self.to_account_id,
            "description": self.description,
            "image_path": self.image_path,
            "tags": self.tags,
            "bill_date": self.bill_date.isoformat() if self.bill_date else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_deleted": self.is_deleted
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> "Bill":
        """从字典创建实例"""
        bill = cls()
        bill.type = data.get("type", BillType.EXPENSE)
        bill.amount = Decimal(str(data.get("amount", 0)))
        bill.category_id = data.get("category_id")
        bill.account_id = data.get("account_id")
        bill.to_account_id = data.get("to_account_id")
        bill.description = data.get("description")
        bill.image_path = data.get("image_path")
        bill.tags = data.get("tags")
        
        # 处理日期时间
        if data.get("bill_date"):
            if isinstance(data["bill_date"], str):
                bill.bill_date = datetime.fromisoformat(data["bill_date"])
            else:
                bill.bill_date = data["bill_date"]
        
        if data.get("created_at"):
            if isinstance(data["created_at"], str):
                bill.created_at = datetime.fromisoformat(data["created_at"])
            else:
                bill.created_at = data["created_at"]
        
        if data.get("updated_at"):
            if isinstance(data["updated_at"], str):
                bill.updated_at = datetime.fromisoformat(data["updated_at"])
            else:
                bill.updated_at = data["updated_at"]
        
        bill.is_deleted = data.get("is_deleted", False)
        
        return bill
