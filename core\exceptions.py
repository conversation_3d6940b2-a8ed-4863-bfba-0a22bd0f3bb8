"""
自定义异常类
"""


class QianJiMiniException(Exception):
    """应用程序基础异常类"""
    
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(self.message)


class DatabaseException(QianJiMiniException):
    """数据库相关异常"""
    pass


class ValidationException(QianJiMiniException):
    """数据验证异常"""
    pass


class ImportException(QianJiMiniException):
    """数据导入异常"""
    pass


class ExportException(QianJiMiniException):
    """数据导出异常"""
    pass


class SyncException(QianJiMiniException):
    """数据同步异常"""
    pass


class ConfigException(QianJiMiniException):
    """配置相关异常"""
    pass


class UIException(QianJiMiniException):
    """UI相关异常"""
    pass
